#!/usr/bin/env python3
"""
Web Interface for Turing USB Display
Provides a simple web interface to upload and display images on the USB display
"""

import os
import io
import base64
from flask import Flask, render_template, request, jsonify, redirect, url_for
from werkzeug.utils import secure_filename
from PIL import Image
from usbdisplay import TuringUSBDisplay

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create uploads directory
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Global display instance
display = None

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def init_display():
    """Initialize the display connection"""
    global display
    try:
        display = TuringUSBDisplay(com_port="AUTO")
        if display.connect():
            return True
        else:
            display = None
            return False
    except Exception as e:
        print(f"Error initializing display: {e}")
        display = None
        return False

@app.route('/')
def index():
    """Main page"""
    display_status = "Connected" if display and display.is_connected else "Disconnected"
    return render_template('index.html', display_status=display_status)

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and display on screen"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file selected'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        try:
            # Read image data
            image_data = file.read()
            image = Image.open(io.BytesIO(image_data))
            
            # Display on screen
            if display and display.is_connected:
                success = display.display_image(image)
                if success:
                    return jsonify({'success': 'Image displayed successfully'})
                else:
                    return jsonify({'error': 'Failed to display image'}), 500
            else:
                return jsonify({'error': 'Display not connected'}), 500
                
        except Exception as e:
            return jsonify({'error': f'Error processing image: {str(e)}'}), 500
    
    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/text', methods=['POST'])
def display_text():
    """Display text on screen"""
    data = request.get_json()
    text = data.get('text', '')
    x = int(data.get('x', 10))
    y = int(data.get('y', 10))
    font_size = int(data.get('font_size', 16))
    
    if not text:
        return jsonify({'error': 'No text provided'}), 400
    
    if display and display.is_connected:
        try:
            success = display.display_text(text, x, y, font_size=font_size)
            if success:
                return jsonify({'success': 'Text displayed successfully'})
            else:
                return jsonify({'error': 'Failed to display text'}), 500
        except Exception as e:
            return jsonify({'error': f'Error displaying text: {str(e)}'}), 500
    else:
        return jsonify({'error': 'Display not connected'}), 500

@app.route('/clear', methods=['POST'])
def clear_screen():
    """Clear the display screen"""
    if display and display.is_connected:
        try:
            success = display.clear_screen()
            if success:
                return jsonify({'success': 'Screen cleared successfully'})
            else:
                return jsonify({'error': 'Failed to clear screen'}), 500
        except Exception as e:
            return jsonify({'error': f'Error clearing screen: {str(e)}'}), 500
    else:
        return jsonify({'error': 'Display not connected'}), 500

@app.route('/brightness', methods=['POST'])
def set_brightness():
    """Set display brightness"""
    data = request.get_json()
    brightness = int(data.get('brightness', 50))
    
    if not 0 <= brightness <= 100:
        return jsonify({'error': 'Brightness must be between 0 and 100'}), 400
    
    if display and display.is_connected:
        try:
            success = display.set_brightness(brightness)
            if success:
                return jsonify({'success': f'Brightness set to {brightness}%'})
            else:
                return jsonify({'error': 'Failed to set brightness'}), 500
        except Exception as e:
            return jsonify({'error': f'Error setting brightness: {str(e)}'}), 500
    else:
        return jsonify({'error': 'Display not connected'}), 500

@app.route('/system_monitor', methods=['POST'])
def show_system_monitor():
    """Display system monitor"""
    if display and display.is_connected:
        try:
            success = display.create_system_monitor_display()
            if success:
                return jsonify({'success': 'System monitor displayed successfully'})
            else:
                return jsonify({'error': 'Failed to display system monitor'}), 500
        except Exception as e:
            return jsonify({'error': f'Error displaying system monitor: {str(e)}'}), 500
    else:
        return jsonify({'error': 'Display not connected'}), 500

@app.route('/status')
def get_status():
    """Get display connection status"""
    status = {
        'connected': display and display.is_connected,
        'port': display.com_port if display else None,
        'width': display.get_width() if display and display.is_connected else None,
        'height': display.get_height() if display and display.is_connected else None
    }
    return jsonify(status)

if __name__ == '__main__':
    print("Initializing Turing USB Display Web Interface...")
    
    # Try to initialize display
    if init_display():
        print("Display connected successfully!")
    else:
        print("Warning: Could not connect to display. Web interface will still start.")
    
    print("Starting web server on http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
