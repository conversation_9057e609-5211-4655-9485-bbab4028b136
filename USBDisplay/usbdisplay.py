#!/usr/bin/env python3
"""
Turing USB Display Interface
A comprehensive interface for Turing USB displays (CT21INCH and similar models)
Based on the turing-smart-screen-python library

Device Information:
- Manufacturer: Turing
- Serial #: CT21INCH
- Vendor ID: 0x1a86 (QinHeng Electronics)
- Product ID: 0xca21
- USB Version: 2.00
- Speed: 12 Mbit/s
"""

import os
import sys
import time
import signal
import logging
from datetime import datetime
from typing import Optional, Tuple, Union
from pathlib import Path

try:
    import serial
    import serial.tools.list_ports
except ImportError:
    print("PySerial not found. Installing...")
    os.system("pip install pyserial")
    import serial
    import serial.tools.list_ports

try:
    from PIL import Image, ImageDraw, ImageFont
except ImportError:
    print("Pillow not found. Installing...")
    os.system("pip install Pillow")
    from PIL import Image, ImageDraw, ImageFont

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TuringDisplayError(Exception):
    """Custom exception for Turing display errors"""
    pass


class Orientation:
    """Display orientation constants"""
    PORTRAIT = 0
    LANDSCAPE = 1
    REVERSE_PORTRAIT = 2
    REVERSE_LANDSCAPE = 3


class TuringUSBDisplay:
    """
    Main interface class for Turing USB displays
    Supports CT21INCH and similar models with QinHeng Electronics USB-to-serial chips
    """

    # Device identification
    VENDOR_ID = 0x1a86  # QinHeng Electronics
    PRODUCT_ID = 0xca21  # Turing CT21INCH

    # Display specifications for CT21INCH (assuming 2.1" display based on model name)
    DEFAULT_WIDTH = 480
    DEFAULT_HEIGHT = 480
    DEFAULT_BAUDRATE = 115200

    # Protocol constants (based on turing-smart-screen-python)
    CMD_RESET = b'\x01'
    CMD_CLEAR = b'\x02'
    CMD_BRIGHTNESS = b'\x03'
    CMD_ORIENTATION = b'\x04'
    CMD_DISPLAY_BITMAP = b'\x05'
    CMD_BACKLIGHT = b'\x06'

    def __init__(self,
                 com_port: str = "AUTO",
                 display_width: int = DEFAULT_WIDTH,
                 display_height: int = DEFAULT_HEIGHT,
                 baudrate: int = DEFAULT_BAUDRATE):
        """
        Initialize the Turing USB Display

        Args:
            com_port: Serial port (e.g., 'COM3', '/dev/ttyUSB0', or 'AUTO' for auto-detection)
            display_width: Display width in pixels
            display_height: Display height in pixels
            baudrate: Serial communication baudrate
        """
        self.com_port = com_port
        self.display_width = display_width
        self.display_height = display_height
        self.baudrate = baudrate
        self.serial_connection: Optional[serial.Serial] = None
        self.is_connected = False
        self.current_orientation = Orientation.PORTRAIT

        # Create resources directory if it doesn't exist
        self.resources_dir = Path(__file__).parent / "resources"
        self.resources_dir.mkdir(exist_ok=True)

        logger.info(f"Initialized Turing USB Display interface")
        logger.info(f"Target device: Vendor ID 0x{self.VENDOR_ID:04x}, Product ID 0x{self.PRODUCT_ID:04x}")

    def find_device_port(self) -> Optional[str]:
        """
        Auto-detect the COM port for the Turing display

        Returns:
            COM port string if found, None otherwise
        """
        logger.info("Scanning for Turing USB display...")

        ports = serial.tools.list_ports.comports()
        for port in ports:
            logger.debug(f"Checking port: {port.device}")
            logger.debug(f"  Description: {port.description}")
            logger.debug(f"  VID: {port.vid}, PID: {port.pid}")

            # Check for exact VID/PID match
            if port.vid == self.VENDOR_ID and port.pid == self.PRODUCT_ID:
                logger.info(f"Found Turing display at {port.device}")
                return port.device

            # Also check for QinHeng Electronics devices (common for Turing displays)
            if port.vid == self.VENDOR_ID:
                logger.info(f"Found QinHeng Electronics device at {port.device} (PID: 0x{port.pid:04x})")
                logger.info("This might be your Turing display with a different PID")
                return port.device

        logger.warning("No Turing display found via USB VID/PID")

        # Fallback: look for common serial device patterns
        common_patterns = [
            "ttyUSB", "ttyACM", "COM", "cu.usbserial", "cu.usbmodem"
        ]

        for port in ports:
            for pattern in common_patterns:
                if pattern in port.device:
                    logger.info(f"Found potential serial device: {port.device}")
                    return port.device

        return None

    def connect(self) -> bool:
        """
        Establish connection to the display

        Returns:
            True if connection successful, False otherwise
        """
        if self.is_connected:
            logger.warning("Already connected to display")
            return True

        # Auto-detect port if needed
        if self.com_port == "AUTO":
            detected_port = self.find_device_port()
            if not detected_port:
                logger.error("Could not auto-detect display port")
                return False
            self.com_port = detected_port

        try:
            logger.info(f"Connecting to display on {self.com_port}...")
            self.serial_connection = serial.Serial(
                port=self.com_port,
                baudrate=self.baudrate,
                timeout=1,
                write_timeout=1
            )

            # Test connection
            time.sleep(0.1)  # Allow connection to stabilize

            if self.serial_connection.is_open:
                self.is_connected = True
                logger.info(f"Successfully connected to display on {self.com_port}")

                # Initialize the display
                self.reset()
                self.initialize_comm()

                return True
            else:
                logger.error("Failed to open serial connection")
                return False

        except serial.SerialException as e:
            logger.error(f"Serial connection error: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during connection: {e}")
            return False

    def disconnect(self):
        """Disconnect from the display"""
        if self.serial_connection and self.serial_connection.is_open:
            try:
                self.serial_connection.close()
                logger.info("Disconnected from display")
            except Exception as e:
                logger.error(f"Error during disconnect: {e}")

        self.is_connected = False
        self.serial_connection = None

    def send_command(self, command: bytes, data: bytes = b'') -> bool:
        """
        Send a command to the display

        Args:
            command: Command byte
            data: Additional data bytes

        Returns:
            True if successful, False otherwise
        """
        if not self.is_connected or not self.serial_connection:
            logger.error("Not connected to display")
            return False

        try:
            # Build command packet
            packet = command + data
            self.serial_connection.write(packet)
            self.serial_connection.flush()
            return True
        except Exception as e:
            logger.error(f"Error sending command: {e}")
            return False

    def reset(self) -> bool:
        """Reset the display"""
        logger.info("Resetting display...")
        return self.send_command(self.CMD_RESET)

    def initialize_comm(self) -> bool:
        """Initialize communication with the display"""
        logger.info("Initializing display communication...")
        # Send initialization sequence
        time.sleep(0.1)
        return True

    def clear_screen(self) -> bool:
        """Clear the display screen"""
        logger.info("Clearing screen...")
        return self.send_command(self.CMD_CLEAR)

    def set_brightness(self, level: int) -> bool:
        """
        Set display brightness

        Args:
            level: Brightness level (0-100)

        Returns:
            True if successful, False otherwise
        """
        if not 0 <= level <= 100:
            logger.error("Brightness level must be between 0 and 100")
            return False

        logger.info(f"Setting brightness to {level}%")
        brightness_data = bytes([level])
        return self.send_command(self.CMD_BRIGHTNESS, brightness_data)

    def set_orientation(self, orientation: int) -> bool:
        """
        Set display orientation

        Args:
            orientation: Orientation constant from Orientation class

        Returns:
            True if successful, False otherwise
        """
        if orientation not in [Orientation.PORTRAIT, Orientation.LANDSCAPE,
                              Orientation.REVERSE_PORTRAIT, Orientation.REVERSE_LANDSCAPE]:
            logger.error("Invalid orientation")
            return False

        logger.info(f"Setting orientation to {orientation}")
        self.current_orientation = orientation
        orientation_data = bytes([orientation])
        return self.send_command(self.CMD_ORIENTATION, orientation_data)

    def get_width(self) -> int:
        """Get current display width based on orientation"""
        if self.current_orientation in [Orientation.LANDSCAPE, Orientation.REVERSE_LANDSCAPE]:
            return max(self.display_width, self.display_height)
        return min(self.display_width, self.display_height)

    def get_height(self) -> int:
        """Get current display height based on orientation"""
        if self.current_orientation in [Orientation.LANDSCAPE, Orientation.REVERSE_LANDSCAPE]:
            return min(self.display_width, self.display_height)
        return max(self.display_width, self.display_height)

    def create_image(self, width: Optional[int] = None, height: Optional[int] = None,
                    color: Tuple[int, int, int] = (0, 0, 0)) -> Image.Image:
        """
        Create a new image with display dimensions

        Args:
            width: Image width (defaults to display width)
            height: Image height (defaults to display height)
            color: Background color as RGB tuple

        Returns:
            PIL Image object
        """
        if width is None:
            width = self.get_width()
        if height is None:
            height = self.get_height()

        return Image.new('RGB', (width, height), color)

    def display_image(self, image: Union[str, Path, Image.Image]) -> bool:
        """
        Display an image on the screen

        Args:
            image: Path to image file or PIL Image object

        Returns:
            True if successful, False otherwise
        """
        try:
            if isinstance(image, (str, Path)):
                img = Image.open(image)
            else:
                img = image

            # Resize image to fit display
            display_size = (self.get_width(), self.get_height())
            img = img.resize(display_size, Image.Resampling.LANCZOS)

            # Convert to RGB if necessary
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # Convert image to bytes (simplified - actual protocol may differ)
            img_bytes = img.tobytes()

            logger.info(f"Displaying image ({img.size[0]}x{img.size[1]})")
            return self.send_command(self.CMD_DISPLAY_BITMAP, img_bytes)

        except Exception as e:
            logger.error(f"Error displaying image: {e}")
            return False

    def display_text(self, text: str, x: int, y: int,
                    font_size: int = 16,
                    font_color: Tuple[int, int, int] = (255, 255, 255),
                    background_color: Optional[Tuple[int, int, int]] = None,
                    font_path: Optional[str] = None) -> bool:
        """
        Display text on the screen

        Args:
            text: Text to display
            x: X coordinate
            y: Y coordinate
            font_size: Font size in pixels
            font_color: Text color as RGB tuple
            background_color: Background color as RGB tuple (None for transparent)
            font_path: Path to TTF font file (None for default)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create image for text
            img = self.create_image()
            draw = ImageDraw.Draw(img)

            # Load font
            try:
                if font_path and os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, font_size)
                else:
                    # Try to use default system font
                    font = ImageFont.load_default()
            except Exception:
                font = ImageFont.load_default()

            # Draw background if specified
            if background_color:
                # Get text bounding box
                bbox = draw.textbbox((x, y), text, font=font)
                draw.rectangle(bbox, fill=background_color)

            # Draw text
            draw.text((x, y), text, fill=font_color, font=font)

            return self.display_image(img)

        except Exception as e:
            logger.error(f"Error displaying text: {e}")
            return False

    def display_progress_bar(self, x: int, y: int, width: int, height: int,
                           value: float, max_value: float = 100.0,
                           bar_color: Tuple[int, int, int] = (0, 255, 0),
                           background_color: Tuple[int, int, int] = (64, 64, 64),
                           border_color: Optional[Tuple[int, int, int]] = None) -> bool:
        """
        Display a progress bar

        Args:
            x: X coordinate
            y: Y coordinate
            width: Bar width
            height: Bar height
            value: Current value
            max_value: Maximum value
            bar_color: Progress bar color
            background_color: Background color
            border_color: Border color (None for no border)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create image for progress bar
            img = self.create_image()
            draw = ImageDraw.Draw(img)

            # Calculate progress
            progress = min(max(value / max_value, 0.0), 1.0)
            fill_width = int(width * progress)

            # Draw background
            draw.rectangle([x, y, x + width, y + height], fill=background_color)

            # Draw progress
            if fill_width > 0:
                draw.rectangle([x, y, x + fill_width, y + height], fill=bar_color)

            # Draw border
            if border_color:
                draw.rectangle([x, y, x + width, y + height], outline=border_color)

            return self.display_image(img)

        except Exception as e:
            logger.error(f"Error displaying progress bar: {e}")
            return False

    def create_system_monitor_display(self) -> bool:
        """
        Create a simple system monitor display

        Returns:
            True if successful, False otherwise
        """
        try:
            import psutil
        except ImportError:
            logger.warning("psutil not available, installing...")
            os.system("pip install psutil")
            import psutil

        try:
            # Create base image
            img = self.create_image(color=(0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Get system information
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # Draw system info
            y_offset = 10
            line_height = 25

            # Title
            draw.text((10, y_offset), "System Monitor", fill=(255, 255, 255))
            y_offset += line_height * 2

            # CPU
            draw.text((10, y_offset), f"CPU: {cpu_percent:.1f}%", fill=(255, 255, 255))
            y_offset += line_height

            # Memory
            memory_percent = memory.percent
            draw.text((10, y_offset), f"RAM: {memory_percent:.1f}%", fill=(255, 255, 255))
            y_offset += line_height

            # Disk
            disk_percent = (disk.used / disk.total) * 100
            draw.text((10, y_offset), f"Disk: {disk_percent:.1f}%", fill=(255, 255, 255))
            y_offset += line_height * 2

            # Progress bars
            bar_width = self.get_width() - 40
            bar_height = 20

            # CPU bar
            self._draw_progress_bar_on_image(draw, 20, y_offset, bar_width, bar_height,
                                           cpu_percent, 100, (255, 0, 0))
            y_offset += bar_height + 10

            # Memory bar
            self._draw_progress_bar_on_image(draw, 20, y_offset, bar_width, bar_height,
                                           memory_percent, 100, (0, 255, 0))
            y_offset += bar_height + 10

            # Disk bar
            self._draw_progress_bar_on_image(draw, 20, y_offset, bar_width, bar_height,
                                           disk_percent, 100, (0, 0, 255))

            return self.display_image(img)

        except Exception as e:
            logger.error(f"Error creating system monitor display: {e}")
            return False

    def _draw_progress_bar_on_image(self, draw, x: int, y: int,
                                   width: int, height: int, value: float, max_value: float,
                                   bar_color: Tuple[int, int, int]):
        """Helper method to draw progress bar on existing image"""
        progress = min(max(value / max_value, 0.0), 1.0)
        fill_width = int(width * progress)

        # Background
        draw.rectangle([x, y, x + width, y + height], fill=(64, 64, 64))

        # Progress
        if fill_width > 0:
            draw.rectangle([x, y, x + fill_width, y + height], fill=bar_color)

        # Border
        draw.rectangle([x, y, x + width, y + height], outline=(128, 128, 128))

    def test_display(self) -> bool:
        """
        Run a test sequence to verify display functionality

        Returns:
            True if all tests pass, False otherwise
        """
        logger.info("Starting display test sequence...")

        try:
            # Test 1: Clear screen
            logger.info("Test 1: Clearing screen...")
            if not self.clear_screen():
                logger.error("Failed to clear screen")
                return False
            time.sleep(1)

            # Test 2: Set brightness
            logger.info("Test 2: Setting brightness...")
            if not self.set_brightness(50):
                logger.error("Failed to set brightness")
                return False
            time.sleep(1)

            # Test 3: Display colored rectangles
            logger.info("Test 3: Displaying colored rectangles...")
            img = self.create_image()
            draw = ImageDraw.Draw(img)

            # Red rectangle
            draw.rectangle([10, 10, 100, 100], fill=(255, 0, 0))
            # Green rectangle
            draw.rectangle([110, 10, 200, 100], fill=(0, 255, 0))
            # Blue rectangle
            draw.rectangle([210, 10, 300, 100], fill=(0, 0, 255))

            if not self.display_image(img):
                logger.error("Failed to display colored rectangles")
                return False
            time.sleep(2)

            # Test 4: Display text
            logger.info("Test 4: Displaying text...")
            if not self.display_text("Hello, Turing Display!", 10, 120, font_size=20):
                logger.error("Failed to display text")
                return False
            time.sleep(2)

            # Test 5: Display progress bars
            logger.info("Test 5: Displaying progress bars...")
            for i in range(0, 101, 10):
                if not self.display_progress_bar(10, 200, 200, 30, i, 100):
                    logger.error("Failed to display progress bar")
                    return False
                time.sleep(0.2)

            # Test 6: System monitor
            logger.info("Test 6: Displaying system monitor...")
            if not self.create_system_monitor_display():
                logger.error("Failed to display system monitor")
                return False

            logger.info("All display tests passed!")
            return True

        except Exception as e:
            logger.error(f"Error during display test: {e}")
            return False

    def __enter__(self):
        """Context manager entry"""
        if self.connect():
            return self
        else:
            raise TuringDisplayError("Failed to connect to display")

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


def main():
    """
    Main function demonstrating the Turing USB Display interface
    """
    import argparse

    parser = argparse.ArgumentParser(description="Turing USB Display Interface")
    parser.add_argument("--port", default="AUTO", help="COM port (default: AUTO)")
    parser.add_argument("--test", action="store_true", help="Run display test sequence")
    parser.add_argument("--monitor", action="store_true", help="Show system monitor")
    parser.add_argument("--brightness", type=int, default=50, help="Set brightness (0-100)")
    parser.add_argument("--text", help="Display custom text")
    parser.add_argument("--image", help="Display image file")
    parser.add_argument("--list-ports", action="store_true", help="List available serial ports")

    args = parser.parse_args()

    if args.list_ports:
        print("Available serial ports:")
        ports = serial.tools.list_ports.comports()
        for port in ports:
            print(f"  {port.device}: {port.description}")
            if port.vid and port.pid:
                print(f"    VID: 0x{port.vid:04x}, PID: 0x{port.pid:04x}")
        return

    # Create display instance
    display = TuringUSBDisplay(com_port=args.port)

    try:
        # Connect to display
        if not display.connect():
            logger.error("Failed to connect to display")
            return

        # Set brightness
        display.set_brightness(args.brightness)

        if args.test:
            # Run test sequence
            display.test_display()
        elif args.monitor:
            # Show system monitor
            logger.info("Displaying system monitor (Ctrl+C to exit)...")
            try:
                while True:
                    display.create_system_monitor_display()
                    time.sleep(2)
            except KeyboardInterrupt:
                logger.info("Stopping system monitor...")
        elif args.text:
            # Display custom text
            display.clear_screen()
            display.display_text(args.text, 10, 10, font_size=24)
            logger.info(f"Displayed text: {args.text}")
        elif args.image:
            # Display image
            if os.path.exists(args.image):
                display.display_image(args.image)
                logger.info(f"Displayed image: {args.image}")
            else:
                logger.error(f"Image file not found: {args.image}")
        else:
            # Default: show welcome message
            display.clear_screen()
            display.display_text("Turing USB Display", 10, 10, font_size=20)
            display.display_text("Interface Ready!", 10, 40, font_size=16)
            logger.info("Display initialized with welcome message")

    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        display.disconnect()


if __name__ == "__main__":
    main()