#!/usr/bin/env python3
"""
GUI Interface for Turing USB Display
Simple tkinter-based interface for controlling the USB display
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import time
from PIL import Image, ImageTk
from usbdisplay import TuringUSBDisplay, Orientation

class TuringDisplayGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Turing USB Display Controller")
        self.root.geometry("600x700")
        
        # Display instance
        self.display = None
        self.is_connected = False
        
        # Create GUI
        self.create_widgets()
        
        # Try to connect on startup
        self.connect_display()
    
    def create_widgets(self):
        """Create the GUI widgets"""
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="🖥️ Turing USB Display Controller", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Connection status
        self.status_frame = ttk.LabelFrame(main_frame, text="Connection Status", padding="10")
        self.status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_label = ttk.Label(self.status_frame, text="Disconnected", foreground="red")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.connect_button = ttk.Button(self.status_frame, text="Connect", command=self.connect_display)
        self.connect_button.grid(row=0, column=1, sticky=tk.E)
        
        # Image upload section
        image_frame = ttk.LabelFrame(main_frame, text="📷 Image Display", padding="10")
        image_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(image_frame, text="Select & Display Image", 
                  command=self.select_and_display_image).grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Text display section
        text_frame = ttk.LabelFrame(main_frame, text="📝 Text Display", padding="10")
        text_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(text_frame, text="Text:").grid(row=0, column=0, sticky=tk.W)
        self.text_entry = scrolledtext.ScrolledText(text_frame, height=3, width=50)
        self.text_entry.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(5, 10))
        
        # Position controls
        ttk.Label(text_frame, text="X:").grid(row=2, column=0, sticky=tk.W)
        self.x_var = tk.StringVar(value="10")
        ttk.Entry(text_frame, textvariable=self.x_var, width=10).grid(row=2, column=1, padx=(5, 10))
        
        ttk.Label(text_frame, text="Y:").grid(row=2, column=2, sticky=tk.W)
        self.y_var = tk.StringVar(value="10")
        ttk.Entry(text_frame, textvariable=self.y_var, width=10).grid(row=2, column=3, padx=(5, 0))
        
        ttk.Label(text_frame, text="Font Size:").grid(row=3, column=0, sticky=tk.W)
        self.font_size_var = tk.StringVar(value="16")
        ttk.Entry(text_frame, textvariable=self.font_size_var, width=10).grid(row=3, column=1, padx=(5, 10))
        
        ttk.Button(text_frame, text="Display Text", 
                  command=self.display_text).grid(row=3, column=2, columnspan=2, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # Display controls section
        control_frame = ttk.LabelFrame(main_frame, text="⚙️ Display Controls", padding="10")
        control_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Brightness control
        ttk.Label(control_frame, text="Brightness:").grid(row=0, column=0, sticky=tk.W)
        self.brightness_var = tk.IntVar(value=50)
        brightness_scale = ttk.Scale(control_frame, from_=0, to=100, variable=self.brightness_var, 
                                   orient=tk.HORIZONTAL, length=200)
        brightness_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))
        
        self.brightness_label = ttk.Label(control_frame, text="50%")
        self.brightness_label.grid(row=0, column=2)
        brightness_scale.configure(command=self.update_brightness_label)
        
        ttk.Button(control_frame, text="Set Brightness", 
                  command=self.set_brightness).grid(row=0, column=3, padx=(10, 0))
        
        # Control buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=1, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Button(button_frame, text="Clear Screen", 
                  command=self.clear_screen).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(button_frame, text="System Monitor", 
                  command=self.show_system_monitor).grid(row=0, column=1, padx=5)
        ttk.Button(button_frame, text="Test Display", 
                  command=self.test_display).grid(row=0, column=2, padx=(5, 0))
        
        # Log section
        log_frame = ttk.LabelFrame(main_frame, text="📋 Log", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
    
    def log_message(self, message):
        """Add a message to the log"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_brightness_label(self, value):
        """Update brightness label"""
        self.brightness_label.config(text=f"{int(float(value))}%")
    
    def connect_display(self):
        """Connect to the display"""
        try:
            self.log_message("Attempting to connect to display...")
            self.display = TuringUSBDisplay(com_port="AUTO")
            
            if self.display.connect():
                self.is_connected = True
                self.status_label.config(text="Connected", foreground="green")
                self.connect_button.config(text="Disconnect", command=self.disconnect_display)
                self.log_message("Successfully connected to display!")
            else:
                self.log_message("Failed to connect to display")
                messagebox.showerror("Connection Error", "Failed to connect to display")
        except Exception as e:
            self.log_message(f"Connection error: {e}")
            messagebox.showerror("Connection Error", f"Error: {e}")
    
    def disconnect_display(self):
        """Disconnect from the display"""
        if self.display:
            self.display.disconnect()
        self.is_connected = False
        self.status_label.config(text="Disconnected", foreground="red")
        self.connect_button.config(text="Connect", command=self.connect_display)
        self.log_message("Disconnected from display")
    
    def check_connection(self):
        """Check if display is connected"""
        if not self.is_connected or not self.display:
            messagebox.showerror("Error", "Display not connected")
            return False
        return True
    
    def select_and_display_image(self):
        """Select and display an image"""
        if not self.check_connection():
            return
        
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.webp"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                self.log_message(f"Displaying image: {file_path}")
                success = self.display.display_image(file_path)
                if success:
                    self.log_message("Image displayed successfully")
                else:
                    self.log_message("Failed to display image")
                    messagebox.showerror("Error", "Failed to display image")
            except Exception as e:
                self.log_message(f"Error displaying image: {e}")
                messagebox.showerror("Error", f"Error displaying image: {e}")
    
    def display_text(self):
        """Display text on the screen"""
        if not self.check_connection():
            return
        
        text = self.text_entry.get("1.0", tk.END).strip()
        if not text:
            messagebox.showerror("Error", "Please enter some text")
            return
        
        try:
            x = int(self.x_var.get())
            y = int(self.y_var.get())
            font_size = int(self.font_size_var.get())
            
            self.log_message(f"Displaying text: '{text}' at ({x}, {y})")
            success = self.display.display_text(text, x, y, font_size=font_size)
            if success:
                self.log_message("Text displayed successfully")
            else:
                self.log_message("Failed to display text")
                messagebox.showerror("Error", "Failed to display text")
        except ValueError:
            messagebox.showerror("Error", "Please enter valid numbers for position and font size")
        except Exception as e:
            self.log_message(f"Error displaying text: {e}")
            messagebox.showerror("Error", f"Error displaying text: {e}")
    
    def set_brightness(self):
        """Set display brightness"""
        if not self.check_connection():
            return
        
        try:
            brightness = self.brightness_var.get()
            self.log_message(f"Setting brightness to {brightness}%")
            success = self.display.set_brightness(brightness)
            if success:
                self.log_message("Brightness set successfully")
            else:
                self.log_message("Failed to set brightness")
                messagebox.showerror("Error", "Failed to set brightness")
        except Exception as e:
            self.log_message(f"Error setting brightness: {e}")
            messagebox.showerror("Error", f"Error setting brightness: {e}")
    
    def clear_screen(self):
        """Clear the display screen"""
        if not self.check_connection():
            return
        
        try:
            self.log_message("Clearing screen...")
            success = self.display.clear_screen()
            if success:
                self.log_message("Screen cleared successfully")
            else:
                self.log_message("Failed to clear screen")
                messagebox.showerror("Error", "Failed to clear screen")
        except Exception as e:
            self.log_message(f"Error clearing screen: {e}")
            messagebox.showerror("Error", f"Error clearing screen: {e}")
    
    def show_system_monitor(self):
        """Show system monitor on display"""
        if not self.check_connection():
            return
        
        try:
            self.log_message("Displaying system monitor...")
            success = self.display.create_system_monitor_display()
            if success:
                self.log_message("System monitor displayed successfully")
            else:
                self.log_message("Failed to display system monitor")
                messagebox.showerror("Error", "Failed to display system monitor")
        except Exception as e:
            self.log_message(f"Error displaying system monitor: {e}")
            messagebox.showerror("Error", f"Error displaying system monitor: {e}")
    
    def test_display(self):
        """Run display test in a separate thread"""
        if not self.check_connection():
            return
        
        def run_test():
            try:
                self.log_message("Starting display test...")
                success = self.display.test_display()
                if success:
                    self.log_message("Display test completed successfully")
                else:
                    self.log_message("Display test failed")
            except Exception as e:
                self.log_message(f"Error during display test: {e}")
        
        # Run test in separate thread to avoid blocking GUI
        threading.Thread(target=run_test, daemon=True).start()

def main():
    """Main function"""
    root = tk.Tk()
    app = TuringDisplayGUI(root)
    
    # Handle window closing
    def on_closing():
        if app.display:
            app.display.disconnect()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
