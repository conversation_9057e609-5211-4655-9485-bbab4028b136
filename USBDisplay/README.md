# Turing USB Display Interface

A comprehensive Python interface for Turing USB displays, specifically designed for the CT21INCH model and similar devices using QinHeng Electronics USB-to-serial chips.

## Device Information

- **Manufacturer**: Turing
- **Model**: CT21INCH
- **Vendor ID**: 0x1a86 (QinHeng Electronics)
- **Product ID**: 0xca21
- **USB Version**: 2.00
- **Speed**: 12 Mbit/s

## Features

- **Auto-detection** of USB display devices
- **Image display** with automatic resizing and format conversion
- **Text rendering** with customizable fonts, colors, and positioning
- **Progress bars** with customizable colors and styles
- **System monitoring** with real-time CPU, memory, and disk usage
- **Multiple orientations** (Portrait, Landscape, Reverse modes)
- **Brightness control**
- **Context manager support** for automatic connection management

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Make sure your USB display is connected to your computer.

## Quick Start

### Basic Usage

```python
from usbdisplay import TuringUSBDisplay

# Create display instance with auto-detection
display = TuringUSBDisplay(com_port="AUTO")

# Connect and display text
if display.connect():
    display.display_text("Hello World!", 10, 10, font_size=24)
    display.disconnect()
```

### Using Context Manager

```python
from usbdisplay import TuringUSBDisplay

# Automatic connection management
with TuringUSBDisplay(com_port="AUTO") as display:
    display.display_text("Hello World!", 10, 10)
    display.create_system_monitor_display()
```

### Command Line Usage

```bash
# List available serial ports
python usbdisplay.py --list-ports

# Run display test sequence
python usbdisplay.py --test

# Show system monitor
python usbdisplay.py --monitor

# Display custom text
python usbdisplay.py --text "Hello World!"

# Display an image
python usbdisplay.py --image path/to/image.jpg

# Set brightness and display text
python usbdisplay.py --brightness 75 --text "Bright Display!"
```

## API Reference

### TuringUSBDisplay Class

#### Constructor
```python
TuringUSBDisplay(com_port="AUTO", display_width=480, display_height=480, baudrate=115200)
```

#### Methods

- `connect()` - Establish connection to the display
- `disconnect()` - Close connection to the display
- `clear_screen()` - Clear the display
- `set_brightness(level)` - Set brightness (0-100)
- `set_orientation(orientation)` - Set display orientation
- `display_image(image)` - Display PIL Image or image file
- `display_text(text, x, y, ...)` - Display text with formatting options
- `display_progress_bar(x, y, width, height, value, max_value, ...)` - Display progress bar
- `create_system_monitor_display()` - Show system statistics
- `test_display()` - Run comprehensive test sequence

## Examples

### Display System Information

```python
with TuringUSBDisplay() as display:
    display.create_system_monitor_display()
```

### Custom Progress Bar

```python
with TuringUSBDisplay() as display:
    for i in range(101):
        display.clear_screen()
        display.display_progress_bar(
            x=10, y=50, width=200, height=30,
            value=i, max_value=100,
            bar_color=(0, 255, 0),
            background_color=(64, 64, 64)
        )
        time.sleep(0.1)
```

### Display Custom Image

```python
from PIL import Image, ImageDraw

with TuringUSBDisplay() as display:
    # Create custom image
    img = display.create_image()
    draw = ImageDraw.Draw(img)
    draw.rectangle([10, 10, 100, 100], fill=(255, 0, 0))
    draw.text((10, 120), "Custom Image", fill=(255, 255, 255))
    
    display.display_image(img)
```

## Troubleshooting

### Device Not Found
- Ensure the USB display is properly connected
- Check if the device appears in system device manager
- Try different USB ports
- Run `python usbdisplay.py --list-ports` to see available devices

### Permission Issues (Linux)
```bash
# Add user to dialout group
sudo usermod -a -G dialout $USER
# Log out and log back in
```

### Connection Issues
- Try specifying the COM port manually instead of "AUTO"
- Check if another application is using the device
- Verify the baudrate matches your device specifications

## Based On

This interface is inspired by and compatible with the [turing-smart-screen-python](https://github.com/mathoudebine/turing-smart-screen-python) project, providing a simplified and focused interface for Turing USB displays.

## License

This project is open source. Feel free to modify and distribute according to your needs.
