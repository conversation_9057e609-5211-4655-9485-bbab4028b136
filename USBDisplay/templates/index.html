<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Turing USB Display Interface</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .file-upload {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .file-upload:hover {
            border-color: #007bff;
        }
        .file-upload.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .slider {
            flex: 1;
        }
        .slider-value {
            min-width: 40px;
            text-align: center;
            font-weight: bold;
        }
        .message {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            display: none;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖥️ Turing USB Display Interface</h1>
        
        <div class="status {{ 'connected' if display_status == 'Connected' else 'disconnected' }}">
            Display Status: {{ display_status }}
        </div>

        <!-- Image Upload Section -->
        <div class="section">
            <h3>📷 Upload Image</h3>
            <div class="file-upload" id="fileUpload">
                <p>Click here or drag and drop an image file</p>
                <input type="file" id="imageFile" accept="image/*" style="display: none;">
            </div>
            <button onclick="uploadImage()" style="margin-top: 10px;">Upload & Display</button>
            <div class="message" id="uploadMessage"></div>
        </div>

        <!-- Text Display Section -->
        <div class="section">
            <h3>📝 Display Text</h3>
            <div class="form-group">
                <label for="textInput">Text:</label>
                <textarea id="textInput" rows="3" placeholder="Enter text to display..."></textarea>
            </div>
            <div style="display: flex; gap: 10px;">
                <div class="form-group" style="flex: 1;">
                    <label for="textX">X Position:</label>
                    <input type="number" id="textX" value="10" min="0">
                </div>
                <div class="form-group" style="flex: 1;">
                    <label for="textY">Y Position:</label>
                    <input type="number" id="textY" value="10" min="0">
                </div>
                <div class="form-group" style="flex: 1;">
                    <label for="fontSize">Font Size:</label>
                    <input type="number" id="fontSize" value="16" min="8" max="72">
                </div>
            </div>
            <button onclick="displayText()">Display Text</button>
            <div class="message" id="textMessage"></div>
        </div>

        <!-- Display Controls Section -->
        <div class="section">
            <h3>⚙️ Display Controls</h3>
            <div class="form-group">
                <label for="brightness">Brightness:</label>
                <div class="slider-container">
                    <input type="range" id="brightness" class="slider" min="0" max="100" value="50" oninput="updateBrightnessValue(this.value)">
                    <span class="slider-value" id="brightnessValue">50%</span>
                </div>
                <button onclick="setBrightness()" style="margin-top: 10px;">Set Brightness</button>
            </div>
            <div style="display: flex; gap: 10px; margin-top: 15px;">
                <button onclick="clearScreen()" class="btn-danger" style="flex: 1;">Clear Screen</button>
                <button onclick="showSystemMonitor()" class="btn-success" style="flex: 1;">System Monitor</button>
            </div>
            <div class="message" id="controlMessage"></div>
        </div>
    </div>

    <script>
        // File upload handling
        const fileUpload = document.getElementById('fileUpload');
        const imageFile = document.getElementById('imageFile');

        fileUpload.addEventListener('click', () => imageFile.click());
        fileUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUpload.classList.add('dragover');
        });
        fileUpload.addEventListener('dragleave', () => {
            fileUpload.classList.remove('dragover');
        });
        fileUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUpload.classList.remove('dragover');
            imageFile.files = e.dataTransfer.files;
        });

        function updateBrightnessValue(value) {
            document.getElementById('brightnessValue').textContent = value + '%';
        }

        function showMessage(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'message ' + (isError ? 'error' : 'success');
            element.style.display = 'block';
            setTimeout(() => {
                element.style.display = 'none';
            }, 5000);
        }

        async function uploadImage() {
            const file = imageFile.files[0];
            if (!file) {
                showMessage('uploadMessage', 'Please select an image file', true);
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('uploadMessage', result.success);
                } else {
                    showMessage('uploadMessage', result.error, true);
                }
            } catch (error) {
                showMessage('uploadMessage', 'Upload failed: ' + error.message, true);
            }
        }

        async function displayText() {
            const text = document.getElementById('textInput').value;
            if (!text.trim()) {
                showMessage('textMessage', 'Please enter some text', true);
                return;
            }

            const data = {
                text: text,
                x: parseInt(document.getElementById('textX').value),
                y: parseInt(document.getElementById('textY').value),
                font_size: parseInt(document.getElementById('fontSize').value)
            };

            try {
                const response = await fetch('/text', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('textMessage', result.success);
                } else {
                    showMessage('textMessage', result.error, true);
                }
            } catch (error) {
                showMessage('textMessage', 'Failed to display text: ' + error.message, true);
            }
        }

        async function setBrightness() {
            const brightness = parseInt(document.getElementById('brightness').value);
            
            try {
                const response = await fetch('/brightness', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ brightness: brightness })
                });
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('controlMessage', result.success);
                } else {
                    showMessage('controlMessage', result.error, true);
                }
            } catch (error) {
                showMessage('controlMessage', 'Failed to set brightness: ' + error.message, true);
            }
        }

        async function clearScreen() {
            try {
                const response = await fetch('/clear', { method: 'POST' });
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('controlMessage', result.success);
                } else {
                    showMessage('controlMessage', result.error, true);
                }
            } catch (error) {
                showMessage('controlMessage', 'Failed to clear screen: ' + error.message, true);
            }
        }

        async function showSystemMonitor() {
            try {
                const response = await fetch('/system_monitor', { method: 'POST' });
                const result = await response.json();
                
                if (response.ok) {
                    showMessage('controlMessage', result.success);
                } else {
                    showMessage('controlMessage', result.error, true);
                }
            } catch (error) {
                showMessage('controlMessage', 'Failed to show system monitor: ' + error.message, true);
            }
        }

        // Auto-refresh status every 10 seconds
        setInterval(async () => {
            try {
                const response = await fetch('/status');
                const status = await response.json();
                const statusElement = document.querySelector('.status');
                
                if (status.connected) {
                    statusElement.textContent = 'Display Status: Connected';
                    statusElement.className = 'status connected';
                } else {
                    statusElement.textContent = 'Display Status: Disconnected';
                    statusElement.className = 'status disconnected';
                }
            } catch (error) {
                console.error('Failed to check status:', error);
            }
        }, 10000);
    </script>
</body>
</html>
